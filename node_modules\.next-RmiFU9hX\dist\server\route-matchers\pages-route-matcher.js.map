{"version": 3, "sources": ["../../../src/server/route-matchers/pages-route-matcher.ts"], "sourcesContent": ["import type { PagesRouteDefinition } from '../route-definitions/pages-route-definition'\nimport { LocaleRouteMatcher } from './locale-route-matcher'\nimport { RouteMatcher } from './route-matcher'\n\nexport class PagesRouteMatcher extends RouteMatcher<PagesRouteDefinition> {}\n\nexport class PagesLocaleRouteMatcher extends LocaleRouteMatcher<PagesRouteDefinition> {}\n"], "names": ["PagesLocaleRouteMatcher", "PagesRouteMatcher", "RouteMatcher", "LocaleRouteMatcher"], "mappings": ";;;;;;;;;;;;;;;IAMaA,uBAAuB;eAAvBA;;IAFAC,iBAAiB;eAAjBA;;;oCAHsB;8BACN;AAEtB,MAAMA,0BAA0BC,0BAAY;AAAwB;AAEpE,MAAMF,gCAAgCG,sCAAkB;AAAwB", "ignoreList": [0]}