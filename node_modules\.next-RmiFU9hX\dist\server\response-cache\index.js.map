{"version": 3, "sources": ["../../../src/server/response-cache/index.ts"], "sourcesContent": ["import type {\n  Response<PERSON>acheEntry,\n  ResponseGenerator,\n  ResponseCacheBase,\n  IncrementalResponseCacheEntry,\n  IncrementalResponseCache,\n} from './types'\n\nimport { Batcher } from '../../lib/batcher'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport {\n  fromResponseCacheEntry,\n  routeKindToIncrementalCacheKind,\n  toResponseCacheEntry,\n} from './utils'\nimport type { RouteK<PERSON> } from '../route-kind'\n\nexport * from './types'\n\nexport default class ResponseCache implements ResponseCacheBase {\n  private readonly batcher = Batcher.create<\n    { key: string; isOnDemandRevalidate: boolean },\n    IncrementalResponseCacheEntry | null,\n    string\n  >({\n    // Ensure on-demand revalidate doesn't block normal requests, it should be\n    // safe to run an on-demand revalidate for the same key as a normal request.\n    cacheKeyFn: ({ key, isOnDemandRevalidate }) =>\n      `${key}-${isOnDemandRevalidate ? '1' : '0'}`,\n    // We wait to do any async work until after we've added our promise to\n    // `pendingResponses` to ensure that any any other calls will reuse the\n    // same promise until we've fully finished our work.\n    schedulerFn: scheduleOnNextTick,\n  })\n\n  private previousCacheItem?: {\n    key: string\n    entry: IncrementalResponseCacheEntry | null\n    expiresAt: number\n  }\n\n  // we don't use minimal_mode name here as this.minimal_mode is\n  // statically replace for server runtimes but we need it to\n  // be dynamic here\n  private minimal_mode?: boolean\n\n  constructor(minimal_mode: boolean) {\n    this.minimal_mode = minimal_mode\n  }\n\n  public async get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      routeKind: RouteKind\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalResponseCache\n      isRoutePPREnabled?: boolean\n      isFallback?: boolean\n      waitUntil?: (prom: Promise<any>) => void\n    }\n  ): Promise<ResponseCacheEntry | null> {\n    // If there is no key for the cache, we can't possibly look this up in the\n    // cache so just return the result of the response generator.\n    if (!key) {\n      return responseGenerator({ hasResolved: false, previousCacheEntry: null })\n    }\n\n    const {\n      incrementalCache,\n      isOnDemandRevalidate = false,\n      isFallback = false,\n      isRoutePPREnabled = false,\n      waitUntil,\n    } = context\n\n    const response = await this.batcher.batch(\n      { key, isOnDemandRevalidate },\n      (cacheKey, resolve) => {\n        const prom = (async () => {\n          // We keep the previous cache entry around to leverage when the\n          // incremental cache is disabled in minimal mode.\n          if (\n            this.minimal_mode &&\n            this.previousCacheItem?.key === cacheKey &&\n            this.previousCacheItem.expiresAt > Date.now()\n          ) {\n            return this.previousCacheItem.entry\n          }\n\n          // Coerce the kindHint into a given kind for the incremental cache.\n          const kind = routeKindToIncrementalCacheKind(context.routeKind)\n\n          let resolved = false\n          let cachedResponse: IncrementalResponseCacheEntry | null = null\n          try {\n            cachedResponse = !this.minimal_mode\n              ? await incrementalCache.get(key, {\n                  kind,\n                  isRoutePPREnabled: context.isRoutePPREnabled,\n                  isFallback,\n                })\n              : null\n\n            if (cachedResponse && !isOnDemandRevalidate) {\n              resolve(cachedResponse)\n              resolved = true\n\n              if (!cachedResponse.isStale || context.isPrefetch) {\n                // The cached value is still valid, so we don't need\n                // to update it yet.\n                return null\n              }\n            }\n\n            const cacheEntry = await responseGenerator({\n              hasResolved: resolved,\n              previousCacheEntry: cachedResponse,\n              isRevalidating: true,\n            })\n\n            // If the cache entry couldn't be generated, we don't want to cache\n            // the result.\n            if (!cacheEntry) {\n              // Unset the previous cache item if it was set.\n              if (this.minimal_mode) this.previousCacheItem = undefined\n              return null\n            }\n\n            const resolveValue = await fromResponseCacheEntry({\n              ...cacheEntry,\n              isMiss: !cachedResponse,\n            })\n            if (!resolveValue) {\n              // Unset the previous cache item if it was set.\n              if (this.minimal_mode) this.previousCacheItem = undefined\n              return null\n            }\n\n            // For on-demand revalidate wait to resolve until cache is set.\n            // Otherwise resolve now.\n            if (!isOnDemandRevalidate && !resolved) {\n              resolve(resolveValue)\n              resolved = true\n            }\n\n            // We want to persist the result only if it has a cache control value\n            // defined.\n            if (resolveValue.cacheControl) {\n              if (this.minimal_mode) {\n                this.previousCacheItem = {\n                  key: cacheKey,\n                  entry: resolveValue,\n                  expiresAt: Date.now() + 1000,\n                }\n              } else {\n                await incrementalCache.set(key, resolveValue.value, {\n                  cacheControl: resolveValue.cacheControl,\n                  isRoutePPREnabled,\n                  isFallback,\n                })\n              }\n            }\n\n            return resolveValue\n          } catch (err) {\n            // When a path is erroring we automatically re-set the existing cache\n            // with new revalidate and expire times to prevent non-stop retrying.\n            if (cachedResponse?.cacheControl) {\n              const newRevalidate = Math.min(\n                Math.max(cachedResponse.cacheControl.revalidate || 3, 3),\n                30\n              )\n\n              const newExpire =\n                cachedResponse.cacheControl.expire === undefined\n                  ? undefined\n                  : Math.max(\n                      newRevalidate + 3,\n                      cachedResponse.cacheControl.expire\n                    )\n\n              await incrementalCache.set(key, cachedResponse.value, {\n                cacheControl: { revalidate: newRevalidate, expire: newExpire },\n                isRoutePPREnabled,\n                isFallback,\n              })\n            }\n\n            // While revalidating in the background we can't reject as we already\n            // resolved the cache entry so log the error here.\n            if (resolved) {\n              console.error(err)\n              return null\n            }\n\n            // We haven't resolved yet, so let's throw to indicate an error.\n            throw err\n          }\n        })()\n\n        // we need to ensure background revalidates are\n        // passed to waitUntil\n        if (waitUntil) {\n          waitUntil(prom)\n        }\n        return prom\n      }\n    )\n\n    return toResponseCacheEntry(response)\n  }\n}\n"], "names": ["ResponseCache", "constructor", "minimal_mode", "batcher", "<PERSON><PERSON>", "create", "cacheKeyFn", "key", "isOnDemandRevalidate", "schedulerFn", "scheduleOnNextTick", "get", "responseGenerator", "context", "hasResolved", "previousCacheEntry", "incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "isRoutePPREnabled", "waitUntil", "response", "batch", "cache<PERSON>ey", "resolve", "prom", "previousCacheItem", "expiresAt", "Date", "now", "entry", "kind", "routeKindToIncrementalCacheKind", "routeKind", "resolved", "cachedResponse", "isStale", "isPrefetch", "cacheEntry", "isRevalidating", "undefined", "resolveValue", "fromResponseCacheEntry", "isMiss", "cacheControl", "set", "value", "err", "newRevalidate", "Math", "min", "max", "revalidate", "newExpire", "expire", "console", "error", "toResponseCacheEntry"], "mappings": ";;;;+BAmBA;;;eAAqBA;;;;yBAXG;2BACW;uBAK5B;qBAGO;;;;;;;;;;;;;;AAEC,MAAMA;IA2BnBC,YAAYC,YAAqB,CAAE;aA1BlBC,UAAUC,gBAAO,CAACC,MAAM,CAIvC;YACA,0EAA0E;YAC1E,4EAA4E;YAC5EC,YAAY,CAAC,EAAEC,GAAG,EAAEC,oBAAoB,EAAE,GACxC,GAAGD,IAAI,CAAC,EAAEC,uBAAuB,MAAM,KAAK;YAC9C,sEAAsE;YACtE,uEAAuE;YACvE,oDAAoD;YACpDC,aAAaC,6BAAkB;QACjC;QAcE,IAAI,CAACR,YAAY,GAAGA;IACtB;IAEA,MAAaS,IACXJ,GAAkB,EAClBK,iBAAoC,EACpCC,OAQC,EACmC;QACpC,0EAA0E;QAC1E,6DAA6D;QAC7D,IAAI,CAACN,KAAK;YACR,OAAOK,kBAAkB;gBAAEE,aAAa;gBAAOC,oBAAoB;YAAK;QAC1E;QAEA,MAAM,EACJC,gBAAgB,EAChBR,uBAAuB,KAAK,EAC5BS,aAAa,KAAK,EAClBC,oBAAoB,KAAK,EACzBC,SAAS,EACV,GAAGN;QAEJ,MAAMO,WAAW,MAAM,IAAI,CAACjB,OAAO,CAACkB,KAAK,CACvC;YAAEd;YAAKC;QAAqB,GAC5B,CAACc,UAAUC;YACT,MAAMC,OAAO,AAAC,CAAA;oBAKV;gBAJF,+DAA+D;gBAC/D,iDAAiD;gBACjD,IACE,IAAI,CAACtB,YAAY,IACjB,EAAA,0BAAA,IAAI,CAACuB,iBAAiB,qBAAtB,wBAAwBlB,GAAG,MAAKe,YAChC,IAAI,CAACG,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;oBACA,OAAO,IAAI,CAACH,iBAAiB,CAACI,KAAK;gBACrC;gBAEA,mEAAmE;gBACnE,MAAMC,OAAOC,IAAAA,sCAA+B,EAAClB,QAAQmB,SAAS;gBAE9D,IAAIC,WAAW;gBACf,IAAIC,iBAAuD;gBAC3D,IAAI;oBACFA,iBAAiB,CAAC,IAAI,CAAChC,YAAY,GAC/B,MAAMc,iBAAiBL,GAAG,CAACJ,KAAK;wBAC9BuB;wBACAZ,mBAAmBL,QAAQK,iBAAiB;wBAC5CD;oBACF,KACA;oBAEJ,IAAIiB,kBAAkB,CAAC1B,sBAAsB;wBAC3Ce,QAAQW;wBACRD,WAAW;wBAEX,IAAI,CAACC,eAAeC,OAAO,IAAItB,QAAQuB,UAAU,EAAE;4BACjD,oDAAoD;4BACpD,oBAAoB;4BACpB,OAAO;wBACT;oBACF;oBAEA,MAAMC,aAAa,MAAMzB,kBAAkB;wBACzCE,aAAamB;wBACblB,oBAAoBmB;wBACpBI,gBAAgB;oBAClB;oBAEA,mEAAmE;oBACnE,cAAc;oBACd,IAAI,CAACD,YAAY;wBACf,+CAA+C;wBAC/C,IAAI,IAAI,CAACnC,YAAY,EAAE,IAAI,CAACuB,iBAAiB,GAAGc;wBAChD,OAAO;oBACT;oBAEA,MAAMC,eAAe,MAAMC,IAAAA,6BAAsB,EAAC;wBAChD,GAAGJ,UAAU;wBACbK,QAAQ,CAACR;oBACX;oBACA,IAAI,CAACM,cAAc;wBACjB,+CAA+C;wBAC/C,IAAI,IAAI,CAACtC,YAAY,EAAE,IAAI,CAACuB,iBAAiB,GAAGc;wBAChD,OAAO;oBACT;oBAEA,+DAA+D;oBAC/D,yBAAyB;oBACzB,IAAI,CAAC/B,wBAAwB,CAACyB,UAAU;wBACtCV,QAAQiB;wBACRP,WAAW;oBACb;oBAEA,qEAAqE;oBACrE,WAAW;oBACX,IAAIO,aAAaG,YAAY,EAAE;wBAC7B,IAAI,IAAI,CAACzC,YAAY,EAAE;4BACrB,IAAI,CAACuB,iBAAiB,GAAG;gCACvBlB,KAAKe;gCACLO,OAAOW;gCACPd,WAAWC,KAAKC,GAAG,KAAK;4BAC1B;wBACF,OAAO;4BACL,MAAMZ,iBAAiB4B,GAAG,CAACrC,KAAKiC,aAAaK,KAAK,EAAE;gCAClDF,cAAcH,aAAaG,YAAY;gCACvCzB;gCACAD;4BACF;wBACF;oBACF;oBAEA,OAAOuB;gBACT,EAAE,OAAOM,KAAK;oBACZ,qEAAqE;oBACrE,qEAAqE;oBACrE,IAAIZ,kCAAAA,eAAgBS,YAAY,EAAE;wBAChC,MAAMI,gBAAgBC,KAAKC,GAAG,CAC5BD,KAAKE,GAAG,CAAChB,eAAeS,YAAY,CAACQ,UAAU,IAAI,GAAG,IACtD;wBAGF,MAAMC,YACJlB,eAAeS,YAAY,CAACU,MAAM,KAAKd,YACnCA,YACAS,KAAKE,GAAG,CACNH,gBAAgB,GAChBb,eAAeS,YAAY,CAACU,MAAM;wBAG1C,MAAMrC,iBAAiB4B,GAAG,CAACrC,KAAK2B,eAAeW,KAAK,EAAE;4BACpDF,cAAc;gCAAEQ,YAAYJ;gCAAeM,QAAQD;4BAAU;4BAC7DlC;4BACAD;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,kDAAkD;oBAClD,IAAIgB,UAAU;wBACZqB,QAAQC,KAAK,CAACT;wBACd,OAAO;oBACT;oBAEA,gEAAgE;oBAChE,MAAMA;gBACR;YACF,CAAA;YAEA,+CAA+C;YAC/C,sBAAsB;YACtB,IAAI3B,WAAW;gBACbA,UAAUK;YACZ;YACA,OAAOA;QACT;QAGF,OAAOgC,IAAAA,2BAAoB,EAACpC;IAC9B;AACF", "ignoreList": [0]}