{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactServerDOMTurbopackServer\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMTurbopackServer"], "mappings": ";AAAAA,OAAOC,OAAO,GAAG,AACfC,QAAQ,yBACRC,QAAQ,CAAC,YAAY,CAAEC,6BAA6B", "ignoreList": [0]}