{"version": 3, "sources": ["../../../src/server/route-matcher-providers/pages-api-route-matcher-provider.ts"], "sourcesContent": ["import { isAPIRoute } from '../../lib/is-api-route'\nimport { PAGES_MANIFEST } from '../../shared/lib/constants'\nimport { RouteKind } from '../route-kind'\nimport {\n  PagesAPILocaleRouteMatcher,\n  PagesAPIRouteMatcher,\n} from '../route-matchers/pages-api-route-matcher'\nimport type {\n  Manife<PERSON>,\n  ManifestLoader,\n} from './helpers/manifest-loaders/manifest-loader'\nimport { ManifestRouteMatcherProvider } from './manifest-route-matcher-provider'\nimport type { I18NProvider } from '../lib/i18n-provider'\nimport { PagesNormalizers } from '../normalizers/built/pages'\n\nexport class PagesAPIRouteMatcherProvider extends ManifestRouteMatcherProvider<PagesAPIRouteMatcher> {\n  private readonly normalizers: PagesNormalizers\n\n  constructor(\n    distDir: string,\n    manifestLoader: ManifestLoader,\n    private readonly i18nProvider?: I18NProvider\n  ) {\n    super(PAGES_MANIFEST, manifestLoader)\n\n    this.normalizers = new PagesNormalizers(distDir)\n  }\n\n  protected async transform(\n    manifest: Manifest\n  ): Promise<ReadonlyArray<PagesAPIRouteMatcher>> {\n    // This matcher is only for Pages API routes.\n    const pathnames = Object.keys(manifest).filter((pathname) =>\n      isAPIRoute(pathname)\n    )\n\n    const matchers: Array<PagesAPIRouteMatcher> = []\n\n    for (const page of pathnames) {\n      if (this.i18nProvider) {\n        // Match the locale on the page name, or default to the default locale.\n        const { detectedLocale, pathname } = this.i18nProvider.analyze(page)\n\n        matchers.push(\n          new PagesAPILocaleRouteMatcher({\n            kind: RouteKind.PAGES_API,\n            pathname,\n            page,\n            bundlePath: this.normalizers.bundlePath.normalize(page),\n            filename: this.normalizers.filename.normalize(manifest[page]),\n            i18n: {\n              locale: detectedLocale,\n            },\n          })\n        )\n      } else {\n        matchers.push(\n          new PagesAPIRouteMatcher({\n            kind: RouteKind.PAGES_API,\n            // In `pages/`, the page is the same as the pathname.\n            pathname: page,\n            page,\n            bundlePath: this.normalizers.bundlePath.normalize(page),\n            filename: this.normalizers.filename.normalize(manifest[page]),\n          })\n        )\n      }\n    }\n\n    return matchers\n  }\n}\n"], "names": ["PagesAPIRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "i18nProvider", "PAGES_MANIFEST", "normalizers", "PagesNormalizers", "transform", "manifest", "pathnames", "Object", "keys", "filter", "pathname", "isAPIRoute", "matchers", "page", "detectedLocale", "analyze", "push", "PagesAPILocaleRouteMatcher", "kind", "RouteKind", "PAGES_API", "bundlePath", "normalize", "filename", "i18n", "locale", "PagesAPIRouteMatcher"], "mappings": ";;;;+BAeaA;;;eAAAA;;;4BAfc;2BACI;2BACL;sCAInB;8CAKsC;uBAEZ;AAE1B,MAAMA,qCAAqCC,0DAA4B;IAG5EC,YACEC,OAAe,EACfC,cAA8B,EAC9B,AAAiBC,YAA2B,CAC5C;QACA,KAAK,CAACC,yBAAc,EAAEF,sBAFLC,eAAAA;QAIjB,IAAI,CAACE,WAAW,GAAG,IAAIC,uBAAgB,CAACL;IAC1C;IAEA,MAAgBM,UACdC,QAAkB,EAC4B;QAC9C,6CAA6C;QAC7C,MAAMC,YAAYC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,WAC9CC,IAAAA,sBAAU,EAACD;QAGb,MAAME,WAAwC,EAAE;QAEhD,KAAK,MAAMC,QAAQP,UAAW;YAC5B,IAAI,IAAI,CAACN,YAAY,EAAE;gBACrB,uEAAuE;gBACvE,MAAM,EAAEc,cAAc,EAAEJ,QAAQ,EAAE,GAAG,IAAI,CAACV,YAAY,CAACe,OAAO,CAACF;gBAE/DD,SAASI,IAAI,CACX,IAAIC,gDAA0B,CAAC;oBAC7BC,MAAMC,oBAAS,CAACC,SAAS;oBACzBV;oBACAG;oBACAQ,YAAY,IAAI,CAACnB,WAAW,CAACmB,UAAU,CAACC,SAAS,CAACT;oBAClDU,UAAU,IAAI,CAACrB,WAAW,CAACqB,QAAQ,CAACD,SAAS,CAACjB,QAAQ,CAACQ,KAAK;oBAC5DW,MAAM;wBACJC,QAAQX;oBACV;gBACF;YAEJ,OAAO;gBACLF,SAASI,IAAI,CACX,IAAIU,0CAAoB,CAAC;oBACvBR,MAAMC,oBAAS,CAACC,SAAS;oBACzB,qDAAqD;oBACrDV,UAAUG;oBACVA;oBACAQ,YAAY,IAAI,CAACnB,WAAW,CAACmB,UAAU,CAACC,SAAS,CAACT;oBAClDU,UAAU,IAAI,CAACrB,WAAW,CAACqB,QAAQ,CAACD,SAAS,CAACjB,QAAQ,CAACQ,KAAK;gBAC9D;YAEJ;QACF;QAEA,OAAOD;IACT;AACF", "ignoreList": [0]}