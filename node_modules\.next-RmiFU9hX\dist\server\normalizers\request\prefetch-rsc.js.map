{"version": 3, "sources": ["../../../../src/server/normalizers/request/prefetch-rsc.ts"], "sourcesContent": ["import type { PathnameNormalizer } from './pathname-normalizer'\n\nimport { RSC_PREFETCH_SUFFIX } from '../../../lib/constants'\nimport { SuffixPathnameNormalizer } from './suffix'\n\nexport class PrefetchRSCPathnameNormalizer\n  extends SuffixPath<PERSON>Normalizer\n  implements PathnameNormalizer\n{\n  constructor() {\n    super(RSC_PREFETCH_SUFFIX)\n  }\n\n  public match(pathname: string): boolean {\n    if (pathname === '/__index' + RSC_PREFETCH_SUFFIX) {\n      return true\n    }\n\n    return super.match(pathname)\n  }\n\n  public normalize(pathname: string, matched?: boolean): string {\n    if (pathname === '/__index' + RSC_PREFETCH_SUFFIX) {\n      return '/'\n    }\n\n    return super.normalize(pathname, matched)\n  }\n}\n"], "names": ["PrefetchRSCPathnameNormalizer", "SuffixPathnameNormalizer", "constructor", "RSC_PREFETCH_SUFFIX", "match", "pathname", "normalize", "matched"], "mappings": ";;;;+BAKaA;;;eAAAA;;;2BAHuB;wBACK;AAElC,MAAMA,sCACHC,gCAAwB;IAGhCC,aAAc;QACZ,KAAK,CAACC,8BAAmB;IAC3B;IAEOC,MAAMC,QAAgB,EAAW;QACtC,IAAIA,aAAa,aAAaF,8BAAmB,EAAE;YACjD,OAAO;QACT;QAEA,OAAO,KAAK,CAACC,MAAMC;IACrB;IAEOC,UAAUD,QAAgB,EAAEE,OAAiB,EAAU;QAC5D,IAAIF,aAAa,aAAaF,8BAAmB,EAAE;YACjD,OAAO;QACT;QAEA,OAAO,KAAK,CAACG,UAAUD,UAAUE;IACnC;AACF", "ignoreList": [0]}