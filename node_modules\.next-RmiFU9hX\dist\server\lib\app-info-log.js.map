{"version": 3, "sources": ["../../../src/server/lib/app-info-log.ts"], "sourcesContent": ["import { loadEnvConfig } from '@next/env'\nimport * as Log from '../../build/output/log'\nimport { bold, purple } from '../../lib/picocolors'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_PRODUCTION_BUILD,\n} from '../../shared/lib/constants'\nimport loadConfig, { type ConfiguredExperimentalFeature } from '../config'\n\nexport function logStartInfo({\n  networkUrl,\n  appUrl,\n  envInfo,\n  experimentalFeatures,\n  maxExperimentalFeatures = Infinity,\n}: {\n  networkUrl: string | null\n  appUrl: string | null\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n  maxExperimentalFeatures?: number\n}) {\n  let bundlerSuffix\n  if (process.env.TURBOPACK) {\n    bundlerSuffix = ' (Turbopack)'\n  } else if (process.env.NEXT_RSPACK) {\n    bundlerSuffix = ' (Rspack)'\n  } else {\n    bundlerSuffix = ''\n  }\n\n  Log.bootstrap(\n    `${bold(\n      purple(`${Log.prefixes.ready} Next.js ${process.env.__NEXT_VERSION}`)\n    )}${bundlerSuffix}`\n  )\n  if (appUrl) {\n    Log.bootstrap(`- Local:        ${appUrl}`)\n  }\n  if (networkUrl) {\n    Log.bootstrap(`- Network:      ${networkUrl}`)\n  }\n  if (envInfo?.length) Log.bootstrap(`- Environments: ${envInfo.join(', ')}`)\n\n  if (experimentalFeatures?.length) {\n    Log.bootstrap(`- Experiments (use with caution):`)\n    // only show a maximum number of flags\n    for (const exp of experimentalFeatures.slice(0, maxExperimentalFeatures)) {\n      const symbol =\n        typeof exp.value === 'boolean'\n          ? exp.value === true\n            ? bold('✓')\n            : bold('⨯')\n          : '·'\n\n      const suffix =\n        typeof exp.value === 'number' || typeof exp.value === 'string'\n          ? `: ${JSON.stringify(exp.value)}`\n          : ''\n\n      const reason = exp.reason ? ` (${exp.reason})` : ''\n\n      Log.bootstrap(`  ${symbol} ${exp.key}${suffix}${reason}`)\n    }\n    /* indicate if there are more than the maximum shown no. flags */\n    if (experimentalFeatures.length > maxExperimentalFeatures) {\n      Log.bootstrap(`  · ...`)\n    }\n  }\n\n  // New line after the bootstrap info\n  Log.info('')\n}\n\nexport async function getStartServerInfo({\n  dir,\n  dev,\n  debugPrerender,\n}: {\n  dir: string\n  dev: boolean\n  debugPrerender?: boolean\n}): Promise<{\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n}> {\n  let experimentalFeatures: ConfiguredExperimentalFeature[] = []\n  await loadConfig(\n    dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_BUILD,\n    dir,\n    {\n      reportExperimentalFeatures(features) {\n        experimentalFeatures = features.sort(\n          ({ key: a }, { key: b }) => a.length - b.length\n        )\n      },\n      debugPrerender,\n    }\n  )\n\n  // we need to reset env if we are going to create\n  // the worker process with the esm loader so that the\n  // initial env state is correct\n  let envInfo: string[] = []\n  const { loadedEnvFiles } = loadEnvConfig(dir, true, console, false)\n  if (loadedEnvFiles.length > 0) {\n    envInfo = loadedEnvFiles.map((f) => f.path)\n  }\n\n  return {\n    envInfo,\n    experimentalFeatures,\n  }\n}\n"], "names": ["getStartServerInfo", "logStartInfo", "networkUrl", "appUrl", "envInfo", "experimentalFeatures", "maxExperimentalFeatures", "Infinity", "bundlerSuffix", "process", "env", "TURBOPACK", "NEXT_RSPACK", "Log", "bootstrap", "bold", "purple", "prefixes", "ready", "__NEXT_VERSION", "length", "join", "exp", "slice", "symbol", "value", "suffix", "JSON", "stringify", "reason", "key", "info", "dir", "dev", "debugPrerender", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "reportExperimentalFeatures", "features", "sort", "a", "b", "loadedEnvFiles", "loadEnvConfig", "console", "map", "f", "path"], "mappings": ";;;;;;;;;;;;;;;IA0EsBA,kBAAkB;eAAlBA;;IAjENC,YAAY;eAAZA;;;qBATc;6DACT;4BACQ;2BAItB;+DACwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExD,SAASA,aAAa,EAC3BC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,oBAAoB,EACpBC,0BAA0BC,QAAQ,EAOnC;IACC,IAAIC;IACJ,IAAIC,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzBH,gBAAgB;IAClB,OAAO,IAAIC,QAAQC,GAAG,CAACE,WAAW,EAAE;QAClCJ,gBAAgB;IAClB,OAAO;QACLA,gBAAgB;IAClB;IAEAK,KAAIC,SAAS,CACX,GAAGC,IAAAA,gBAAI,EACLC,IAAAA,kBAAM,EAAC,GAAGH,KAAII,QAAQ,CAACC,KAAK,CAAC,SAAS,EAAET,QAAQC,GAAG,CAACS,cAAc,EAAE,KAClEX,eAAe;IAErB,IAAIL,QAAQ;QACVU,KAAIC,SAAS,CAAC,CAAC,gBAAgB,EAAEX,QAAQ;IAC3C;IACA,IAAID,YAAY;QACdW,KAAIC,SAAS,CAAC,CAAC,gBAAgB,EAAEZ,YAAY;IAC/C;IACA,IAAIE,2BAAAA,QAASgB,MAAM,EAAEP,KAAIC,SAAS,CAAC,CAAC,gBAAgB,EAAEV,QAAQiB,IAAI,CAAC,OAAO;IAE1E,IAAIhB,wCAAAA,qBAAsBe,MAAM,EAAE;QAChCP,KAAIC,SAAS,CAAC,CAAC,iCAAiC,CAAC;QACjD,sCAAsC;QACtC,KAAK,MAAMQ,OAAOjB,qBAAqBkB,KAAK,CAAC,GAAGjB,yBAA0B;YACxE,MAAMkB,SACJ,OAAOF,IAAIG,KAAK,KAAK,YACjBH,IAAIG,KAAK,KAAK,OACZV,IAAAA,gBAAI,EAAC,OACLA,IAAAA,gBAAI,EAAC,OACP;YAEN,MAAMW,SACJ,OAAOJ,IAAIG,KAAK,KAAK,YAAY,OAAOH,IAAIG,KAAK,KAAK,WAClD,CAAC,EAAE,EAAEE,KAAKC,SAAS,CAACN,IAAIG,KAAK,GAAG,GAChC;YAEN,MAAMI,SAASP,IAAIO,MAAM,GAAG,CAAC,EAAE,EAAEP,IAAIO,MAAM,CAAC,CAAC,CAAC,GAAG;YAEjDhB,KAAIC,SAAS,CAAC,CAAC,EAAE,EAAEU,OAAO,CAAC,EAAEF,IAAIQ,GAAG,GAAGJ,SAASG,QAAQ;QAC1D;QACA,+DAA+D,GAC/D,IAAIxB,qBAAqBe,MAAM,GAAGd,yBAAyB;YACzDO,KAAIC,SAAS,CAAC,CAAC,OAAO,CAAC;QACzB;IACF;IAEA,oCAAoC;IACpCD,KAAIkB,IAAI,CAAC;AACX;AAEO,eAAe/B,mBAAmB,EACvCgC,GAAG,EACHC,GAAG,EACHC,cAAc,EAKf;IAIC,IAAI7B,uBAAwD,EAAE;IAC9D,MAAM8B,IAAAA,eAAU,EACdF,MAAMG,mCAAwB,GAAGC,iCAAsB,EACvDL,KACA;QACEM,4BAA2BC,QAAQ;YACjClC,uBAAuBkC,SAASC,IAAI,CAClC,CAAC,EAAEV,KAAKW,CAAC,EAAE,EAAE,EAAEX,KAAKY,CAAC,EAAE,GAAKD,EAAErB,MAAM,GAAGsB,EAAEtB,MAAM;QAEnD;QACAc;IACF;IAGF,iDAAiD;IACjD,qDAAqD;IACrD,+BAA+B;IAC/B,IAAI9B,UAAoB,EAAE;IAC1B,MAAM,EAAEuC,cAAc,EAAE,GAAGC,IAAAA,kBAAa,EAACZ,KAAK,MAAMa,SAAS;IAC7D,IAAIF,eAAevB,MAAM,GAAG,GAAG;QAC7BhB,UAAUuC,eAAeG,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI;IAC5C;IAEA,OAAO;QACL5C;QACAC;IACF;AACF", "ignoreList": [0]}