{"version": 3, "sources": ["../../../../src/server/normalizers/request/segment-prefix-rsc.ts"], "sourcesContent": ["import type { PathnameNormalizer } from './pathname-normalizer'\n\nimport {\n  RSC_SEGMENT_SUFFIX,\n  RSC_SEGMENTS_DIR_SUFFIX,\n} from '../../../lib/constants'\n\nconst PATTERN = new RegExp(\n  `^(/.*)${RSC_SEGMENTS_DIR_SUFFIX}(/.*)${RSC_SEGMENT_SUFFIX}$`\n)\n\nexport class SegmentPrefixRSCPathnameNormalizer implements PathnameNormalizer {\n  public match(pathname: string): boolean {\n    return PATTERN.test(pathname)\n  }\n\n  public extract(pathname: string) {\n    const match = pathname.match(PATTERN)\n    if (!match) return null\n\n    return { originalPathname: match[1], segmentPath: match[2] }\n  }\n\n  public normalize(pathname: string): string {\n    const match = this.extract(pathname)\n    if (!match) return pathname\n\n    return match.originalPathname\n  }\n}\n"], "names": ["SegmentPrefixRSCPathnameNormalizer", "PATTERN", "RegExp", "RSC_SEGMENTS_DIR_SUFFIX", "RSC_SEGMENT_SUFFIX", "match", "pathname", "test", "extract", "originalPathname", "segmentPath", "normalize"], "mappings": ";;;;+BAWaA;;;eAAAA;;;2BANN;AAEP,MAAMC,UAAU,IAAIC,OAClB,CAAC,MAAM,EAAEC,kCAAuB,CAAC,KAAK,EAAEC,6BAAkB,CAAC,CAAC,CAAC;AAGxD,MAAMJ;IACJK,MAAMC,QAAgB,EAAW;QACtC,OAAOL,QAAQM,IAAI,CAACD;IACtB;IAEOE,QAAQF,QAAgB,EAAE;QAC/B,MAAMD,QAAQC,SAASD,KAAK,CAACJ;QAC7B,IAAI,CAACI,OAAO,OAAO;QAEnB,OAAO;YAAEI,kBAAkBJ,KAAK,CAAC,EAAE;YAAEK,aAAaL,KAAK,CAAC,EAAE;QAAC;IAC7D;IAEOM,UAAUL,QAAgB,EAAU;QACzC,MAAMD,QAAQ,IAAI,CAACG,OAAO,CAACF;QAC3B,IAAI,CAACD,OAAO,OAAOC;QAEnB,OAAOD,MAAMI,gBAAgB;IAC/B;AACF", "ignoreList": [0]}