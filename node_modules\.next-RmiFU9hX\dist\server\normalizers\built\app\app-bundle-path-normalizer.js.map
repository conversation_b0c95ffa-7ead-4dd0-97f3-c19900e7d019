{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-bundle-path-normalizer.ts"], "sourcesContent": ["import { Normalizers } from '../../normalizers'\nimport type { Normalizer } from '../../normalizer'\nimport { PrefixingNormalizer } from '../../prefixing-normalizer'\nimport { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\nimport { UnderscoreNormalizer } from '../../underscore-normalizer'\n\nexport class AppBundlePathNormalizer extends PrefixingNormalizer {\n  constructor() {\n    super('app')\n  }\n\n  public normalize(page: string): string {\n    return super.normalize(normalizePagePath(page))\n  }\n}\n\nexport class DevAppBundlePathNormalizer extends Normalizers {\n  constructor(pageNormalizer: Normalizer, isTurbopack: boolean) {\n    const normalizers = [\n      // This should normalize the filename to a page.\n      pageNormalizer,\n      // Normalize the app page to a pathname.\n      new AppBundlePathNormalizer(),\n    ]\n\n    // %5F to _ replacement should only happen with Turbopack.\n    if (isTurbopack) {\n      normalizers.unshift(new UnderscoreNormalizer())\n    }\n    super(normalizers)\n  }\n\n  public normalize(filename: string): string {\n    return super.normalize(filename)\n  }\n}\n"], "names": ["AppBundlePathNormalizer", "DevAppBundlePathNormalizer", "PrefixingNormalizer", "constructor", "normalize", "page", "normalizePagePath", "Normalizers", "pageNormalizer", "isTurbopack", "normalizers", "unshift", "UnderscoreNormalizer", "filename"], "mappings": ";;;;;;;;;;;;;;;IAMaA,uBAAuB;eAAvBA;;IAUAC,0BAA0B;eAA1BA;;;6BAhBe;qCAEQ;mCACF;sCACG;AAE9B,MAAMD,gCAAgCE,wCAAmB;IAC9DC,aAAc;QACZ,KAAK,CAAC;IACR;IAEOC,UAAUC,IAAY,EAAU;QACrC,OAAO,KAAK,CAACD,UAAUE,IAAAA,oCAAiB,EAACD;IAC3C;AACF;AAEO,MAAMJ,mCAAmCM,wBAAW;IACzDJ,YAAYK,cAA0B,EAAEC,WAAoB,CAAE;QAC5D,MAAMC,cAAc;YAClB,gDAAgD;YAChDF;YACA,wCAAwC;YACxC,IAAIR;SACL;QAED,0DAA0D;QAC1D,IAAIS,aAAa;YACfC,YAAYC,OAAO,CAAC,IAAIC,0CAAoB;QAC9C;QACA,KAAK,CAACF;IACR;IAEON,UAAUS,QAAgB,EAAU;QACzC,OAAO,KAAK,CAACT,UAAUS;IACzB;AACF", "ignoreList": [0]}