{"version": 3, "sources": ["../../../../../../src/server/route-modules/app-page/vendored/ssr/entrypoints.ts"], "sourcesContent": ["import * as React from 'react'\nimport * as ReactDOM from 'react-dom'\nimport * as ReactJsxDevRuntime from 'react/jsx-dev-runtime'\nimport * as ReactJsxRuntime from 'react/jsx-runtime'\nimport * as ReactCompilerRuntime from 'react/compiler-runtime'\n\nimport * as ReactDOMServer from 'react-dom/server'\n\nfunction getAltProxyForBindingsDEV(\n  type: 'Turbopack' | 'Webpack',\n  pkg: 'react-server-dom-turbopack/client' | 'react-server-dom-webpack/client'\n) {\n  if (process.env.NODE_ENV === 'development') {\n    const altType = type === 'Turbopack' ? 'Webpack' : 'Turbopack'\n    const altPkg = pkg.replace(new RegExp(type, 'gi'), altType.toLowerCase())\n\n    return new Proxy(\n      {},\n      {\n        get(_, prop: string) {\n          throw new Error(\n            `Expected to use ${type} bindings (${pkg}) for React but the current process is referencing '${prop}' from the ${altType} bindings (${altPkg}). This is likely a bug in our integration of the Next.js server runtime.`\n          )\n        },\n      }\n    )\n  }\n}\n\nlet ReactServerDOMTurbopackClient, ReactServerDOMWebpackClient\nif (process.env.TURBOPACK) {\n  ReactServerDOMTurbopackClient =\n    // @ts-expect-error -- TODO: Add types\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-turbopack/client') as typeof import('react-server-dom-turbopack/client')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMWebpackClient = getAltProxyForBindingsDEV(\n      'Turbopack',\n      'react-server-dom-turbopack/client'\n    )\n  }\n} else {\n  ReactServerDOMWebpackClient =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/client') as typeof import('react-server-dom-webpack/client')\n  if (process.env.NODE_ENV === 'development') {\n    ReactServerDOMTurbopackClient = getAltProxyForBindingsDEV(\n      'Webpack',\n      'react-server-dom-webpack/client'\n    )\n  }\n}\n\nexport {\n  React,\n  ReactJsxDevRuntime,\n  ReactJsxRuntime,\n  ReactCompilerRuntime,\n  ReactDOM,\n  ReactDOMServer,\n  ReactServerDOMTurbopackClient,\n  ReactServerDOMWebpackClient,\n}\n"], "names": ["React", "ReactCompilerRuntime", "ReactDOM", "ReactDOMServer", "ReactJsxDevRuntime", "ReactJsxRuntime", "ReactServerDOMTurbopackClient", "ReactServerDOMWebpackClient", "getAltProxyForBindingsDEV", "type", "pkg", "process", "env", "NODE_ENV", "altType", "altPkg", "replace", "RegExp", "toLowerCase", "Proxy", "get", "_", "prop", "Error", "TURBOPACK", "require"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAsDEA,KAAK;eAALA;;IAGAC,oBAAoB;eAApBA;;IACAC,QAAQ;eAARA;;IACAC,cAAc;eAAdA;;IAJAC,kBAAkB;eAAlBA;;IACAC,eAAe;eAAfA;;IAIAC,6BAA6B;eAA7BA;;IACAC,2BAA2B;eAA3BA;;;+DA7DqB;kEACG;uEACU;oEACH;yEACK;gEAEN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhC,SAASC,0BACPC,IAA6B,EAC7BC,GAA4E;IAE5E,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAMC,UAAUL,SAAS,cAAc,YAAY;QACnD,MAAMM,SAASL,IAAIM,OAAO,CAAC,IAAIC,OAAOR,MAAM,OAAOK,QAAQI,WAAW;QAEtE,OAAO,IAAIC,MACT,CAAC,GACD;YACEC,KAAIC,CAAC,EAAEC,IAAY;gBACjB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,gBAAgB,EAAEd,KAAK,WAAW,EAAEC,IAAI,oDAAoD,EAAEY,KAAK,WAAW,EAAER,QAAQ,WAAW,EAAEC,OAAO,yEAAyE,CAAC,GADnN,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IAEJ;AACF;AAEA,IAAIT,+BAA+BC;AACnC,IAAII,QAAQC,GAAG,CAACY,SAAS,EAAE;IACzBlB,gCACE,sCAAsC;IACtC,6DAA6D;IAC7DmB,QAAQ;IACV,IAAId,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CN,8BAA8BC,0BAC5B,aACA;IAEJ;AACF,OAAO;IACLD,8BACE,6DAA6D;IAC7DkB,QAAQ;IACV,IAAId,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CP,gCAAgCE,0BAC9B,WACA;IAEJ;AACF", "ignoreList": [0]}