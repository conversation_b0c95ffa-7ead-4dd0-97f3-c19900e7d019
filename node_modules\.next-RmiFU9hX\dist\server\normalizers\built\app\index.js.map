{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/index.ts"], "sourcesContent": ["import {\n  AppBundlePath<PERSON>ormalizer,\n  DevAppBundlePathNormalizer,\n} from './app-bundle-path-normalizer'\nimport { AppFilenameNormalizer } from './app-filename-normalizer'\nimport { DevAppPageNormalizer } from './app-page-normalizer'\nimport {\n  App<PERSON><PERSON><PERSON><PERSON>ormali<PERSON>,\n  DevAppPathnameNormalizer,\n} from './app-pathname-normalizer'\n\nexport class AppNormalizers {\n  public readonly filename: AppFilenameNormalizer\n  public readonly pathname: AppPathnameNormalizer\n  public readonly bundlePath: AppBundlePathNormalizer\n\n  constructor(distDir: string) {\n    this.filename = new AppFilenameNormalizer(distDir)\n    this.pathname = new AppPathnameNormalizer()\n    this.bundlePath = new AppBundlePathNormalizer()\n  }\n}\n\nexport class DevAppNormalizers {\n  public readonly page: DevAppPageNormalizer\n  public readonly pathname: DevAppPathnameNormalizer\n  public readonly bundlePath: DevAppBundlePathNormalizer\n\n  constructor(\n    appDir: string,\n    extensions: ReadonlyArray<string>,\n    isTurbopack: boolean\n  ) {\n    this.page = new DevAppPageNormalizer(appDir, extensions, isTurbopack)\n    this.pathname = new DevAppPathnameNormalizer(this.page)\n    this.bundlePath = new DevAppBundlePathNormalizer(this.page, isTurbopack)\n  }\n}\n"], "names": ["AppNormalizers", "DevAppNormalizers", "constructor", "distDir", "filename", "AppFilenameNormalizer", "pathname", "AppPathnameNormalizer", "bundlePath", "AppBundlePathNormalizer", "appDir", "extensions", "isTurbopack", "page", "DevAppPageNormalizer", "DevAppPathnameNormalizer", "DevAppBundlePathNormalizer"], "mappings": ";;;;;;;;;;;;;;;IAWaA,cAAc;eAAdA;;IAYAC,iBAAiB;eAAjBA;;;yCApBN;uCAC+B;mCACD;uCAI9B;AAEA,MAAMD;IAKXE,YAAYC,OAAe,CAAE;QAC3B,IAAI,CAACC,QAAQ,GAAG,IAAIC,4CAAqB,CAACF;QAC1C,IAAI,CAACG,QAAQ,GAAG,IAAIC,4CAAqB;QACzC,IAAI,CAACC,UAAU,GAAG,IAAIC,gDAAuB;IAC/C;AACF;AAEO,MAAMR;IAKXC,YACEQ,MAAc,EACdC,UAAiC,EACjCC,WAAoB,CACpB;QACA,IAAI,CAACC,IAAI,GAAG,IAAIC,uCAAoB,CAACJ,QAAQC,YAAYC;QACzD,IAAI,CAACN,QAAQ,GAAG,IAAIS,+CAAwB,CAAC,IAAI,CAACF,IAAI;QACtD,IAAI,CAACL,UAAU,GAAG,IAAIQ,mDAA0B,CAAC,IAAI,CAACH,IAAI,EAAED;IAC9D;AACF", "ignoreList": [0]}