{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-page-normalizer.ts"], "sourcesContent": ["import { PAGE_TYPES } from '../../../../lib/page-types'\nimport { AbsoluteFilenameNormalizer } from '../../absolute-filename-normalizer'\nimport { Normalizers } from '../../normalizers'\n\n/**\n * DevAppPageNormalizer is a normalizer that is used to normalize a pathname\n * to a page in the `app` directory.\n */\nclass DevAppPageNormalizerInternal extends AbsoluteFilenameNormalizer {\n  constructor(appDir: string, extensions: ReadonlyArray<string>) {\n    super(appDir, extensions, PAGE_TYPES.APP)\n  }\n}\n\nexport class DevAppPageNormalizer extends Normalizers {\n  constructor(\n    appDir: string,\n    extensions: ReadonlyArray<string>,\n    _isTurbopack: boolean\n  ) {\n    const normalizer = new DevAppPageNormalizerInternal(appDir, extensions)\n    super(\n      // %5F to _ replacement should only happen with Turbopack.\n      // TODO: enable when page matcher `/_` check is moved: https://github.com/vercel/next.js/blob/8eda00bf5999e43e8f0211bd72c981d5ce292e8b/packages/next/src/server/route-matcher-providers/dev/dev-app-route-route-matcher-provider.ts#L48\n      // isTurbopack\n      //   ? [\n      //       // The page should have the `%5F` characters replaced with `_` characters.\n      //       new UnderscoreNormalizer(),\n      //       normalizer,\n      //     ]\n      //   : [normalizer]\n      [normalizer]\n    )\n  }\n}\n"], "names": ["DevAppPageNormalizer", "DevAppPageNormalizerInternal", "AbsoluteFilenameNormalizer", "constructor", "appDir", "extensions", "PAGE_TYPES", "APP", "Normalizers", "_isTurbopack", "normalizer"], "mappings": ";;;;+BAcaA;;;eAAAA;;;2BAdc;4CACgB;6BACf;AAE5B;;;CAGC,GACD,MAAMC,qCAAqCC,sDAA0B;IACnEC,YAAYC,MAAc,EAAEC,UAAiC,CAAE;QAC7D,KAAK,CAACD,QAAQC,YAAYC,qBAAU,CAACC,GAAG;IAC1C;AACF;AAEO,MAAMP,6BAA6BQ,wBAAW;IACnDL,YACEC,MAAc,EACdC,UAAiC,EACjCI,YAAqB,CACrB;QACA,MAAMC,aAAa,IAAIT,6BAA6BG,QAAQC;QAC5D,KAAK,CACH,0DAA0D;QAC1D,uOAAuO;QACvO,cAAc;QACd,QAAQ;QACR,mFAAmF;QACnF,oCAAoC;QACpC,oBAAoB;QACpB,QAAQ;QACR,mBAAmB;QACnB;YAACK;SAAW;IAEhB;AACF", "ignoreList": [0]}