{"version": 3, "sources": ["../../../src/server/node-environment-extensions/utils.tsx"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortOnSynchronousPlatformIOAccess,\n  trackSynchronousPlatformIOAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\ntype ApiType = 'time' | 'random' | 'crypto'\n\nexport function io(expression: string, type: ApiType) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    let isClient = false\n    if (\n      workUnitStore.type === 'prerender' ||\n      (isClient = workUnitStore.type === 'prerender-client')\n    ) {\n      const prerenderSignal = workUnitStore.controller.signal\n      if (prerenderSignal.aborted === false) {\n        // If the prerender signal is already aborted we don't need to construct any stacks\n        // because something else actually terminated the prerender.\n        const workStore = workAsyncStorage.getStore()\n        if (workStore) {\n          let message: string\n          switch (type) {\n            case 'time':\n              message = isClient\n                ? `Route \"${workStore.route}\" used ${expression} inside a Client Component without a Suspense boundary above it. See more info here: https://nextjs.org/docs/messages/next-prerender-current-time-client`\n                : `Route \"${workStore.route}\" used ${expression} instead of using \\`performance\\` or without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-current-time`\n              break\n            case 'random':\n              message = isClient\n                ? `Route \"${workStore.route}\" used ${expression} inside a Client Component without a Suspense boundary above it. See more info here: https://nextjs.org/docs/messages/next-prerender-random-client`\n                : `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-random`\n              break\n            case 'crypto':\n              message = isClient\n                ? `Route \"${workStore.route}\" used ${expression} inside a Client Component without a Suspense boundary above it. See more info here: https://nextjs.org/docs/messages/next-prerender-crypto-client`\n                : `Route \"${workStore.route}\" used ${expression} outside of \\`\"use cache\"\\` and without explicitly calling \\`await connection()\\` beforehand. See more info here: https://nextjs.org/docs/messages/next-prerender-crypto`\n              break\n            default:\n              throw new InvariantError(\n                'Unknown expression type in abortOnSynchronousPlatformIOAccess.'\n              )\n          }\n\n          const errorWithStack = new Error(message)\n\n          if (\n            process.env.NODE_ENV !== 'production' &&\n            workUnitStore.captureOwnerStack\n          ) {\n            const ownerStack = workUnitStore.captureOwnerStack()\n\n            if (ownerStack) {\n              // TODO: Instead of stitching the stacks here, we should log the\n              // original error as-is when it occurs (i.e. here), and let\n              // `patchErrorInspect` handle adding the owner stack, instead of\n              // logging it deferred in the `LogSafely` component via\n              // `throwIfDisallowedDynamic`.\n              applyOwnerStack(errorWithStack, ownerStack)\n            }\n          }\n\n          abortOnSynchronousPlatformIOAccess(\n            workStore.route,\n            expression,\n            errorWithStack,\n            workUnitStore\n          )\n        }\n      }\n    } else if (\n      workUnitStore.type === 'request' &&\n      workUnitStore.prerenderPhase === true\n    ) {\n      const requestStore = workUnitStore\n      trackSynchronousPlatformIOAccessInDev(requestStore)\n    }\n  }\n}\n\nfunction applyOwnerStack(error: Error, ownerStack: string) {\n  let stack = ownerStack\n\n  if (error.stack) {\n    const frames: string[] = []\n\n    for (const frame of error.stack.split('\\n').slice(1)) {\n      if (frame.includes('react_stack_bottom_frame')) {\n        break\n      }\n\n      frames.push(frame)\n    }\n\n    stack = '\\n' + frames.join('\\n') + stack\n  }\n\n  error.stack = error.name + ': ' + error.message + stack\n}\n"], "names": ["io", "expression", "type", "workUnitStore", "workUnitAsyncStorage", "getStore", "isClient", "prerenderSignal", "controller", "signal", "aborted", "workStore", "workAsyncStorage", "message", "route", "InvariantError", "errorWithStack", "Error", "process", "env", "NODE_ENV", "captureOwnerStack", "ownerStack", "applyOwnerStack", "abortOnSynchronousPlatformIOAccess", "prerenderPhase", "requestStore", "trackSynchronousPlatformIOAccessInDev", "error", "stack", "frames", "frame", "split", "slice", "includes", "push", "join", "name"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;0CAViB;8CACI;kCAI9B;gCACwB;AAIxB,SAASA,GAAGC,UAAkB,EAAEC,IAAa;IAClD,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,IAAIG,WAAW;QACf,IACEH,cAAcD,IAAI,KAAK,eACtBI,CAAAA,WAAWH,cAAcD,IAAI,KAAK,kBAAiB,GACpD;YACA,MAAMK,kBAAkBJ,cAAcK,UAAU,CAACC,MAAM;YACvD,IAAIF,gBAAgBG,OAAO,KAAK,OAAO;gBACrC,mFAAmF;gBACnF,4DAA4D;gBAC5D,MAAMC,YAAYC,0CAAgB,CAACP,QAAQ;gBAC3C,IAAIM,WAAW;oBACb,IAAIE;oBACJ,OAAQX;wBACN,KAAK;4BACHW,UAAUP,WACN,CAAC,OAAO,EAAEK,UAAUG,KAAK,CAAC,OAAO,EAAEb,WAAW,wJAAwJ,CAAC,GACvM,CAAC,OAAO,EAAEU,UAAUG,KAAK,CAAC,OAAO,EAAEb,WAAW,mLAAmL,CAAC;4BACtO;wBACF,KAAK;4BACHY,UAAUP,WACN,CAAC,OAAO,EAAEK,UAAUG,KAAK,CAAC,OAAO,EAAEb,WAAW,kJAAkJ,CAAC,GACjM,CAAC,OAAO,EAAEU,UAAUG,KAAK,CAAC,OAAO,EAAEb,WAAW,wKAAwK,CAAC;4BAC3N;wBACF,KAAK;4BACHY,UAAUP,WACN,CAAC,OAAO,EAAEK,UAAUG,KAAK,CAAC,OAAO,EAAEb,WAAW,kJAAkJ,CAAC,GACjM,CAAC,OAAO,EAAEU,UAAUG,KAAK,CAAC,OAAO,EAAEb,WAAW,wKAAwK,CAAC;4BAC3N;wBACF;4BACE,MAAM,qBAEL,CAFK,IAAIc,8BAAc,CACtB,mEADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;oBACJ;oBAEA,MAAMC,iBAAiB,qBAAkB,CAAlB,IAAIC,MAAMJ,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAExC,IACEK,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBjB,cAAckB,iBAAiB,EAC/B;wBACA,MAAMC,aAAanB,cAAckB,iBAAiB;wBAElD,IAAIC,YAAY;4BACd,gEAAgE;4BAChE,2DAA2D;4BAC3D,gEAAgE;4BAChE,uDAAuD;4BACvD,8BAA8B;4BAC9BC,gBAAgBP,gBAAgBM;wBAClC;oBACF;oBAEAE,IAAAA,oDAAkC,EAChCb,UAAUG,KAAK,EACfb,YACAe,gBACAb;gBAEJ;YACF;QACF,OAAO,IACLA,cAAcD,IAAI,KAAK,aACvBC,cAAcsB,cAAc,KAAK,MACjC;YACA,MAAMC,eAAevB;YACrBwB,IAAAA,uDAAqC,EAACD;QACxC;IACF;AACF;AAEA,SAASH,gBAAgBK,KAAY,EAAEN,UAAkB;IACvD,IAAIO,QAAQP;IAEZ,IAAIM,MAAMC,KAAK,EAAE;QACf,MAAMC,SAAmB,EAAE;QAE3B,KAAK,MAAMC,SAASH,MAAMC,KAAK,CAACG,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAI;YACpD,IAAIF,MAAMG,QAAQ,CAAC,6BAA6B;gBAC9C;YACF;YAEAJ,OAAOK,IAAI,CAACJ;QACd;QAEAF,QAAQ,OAAOC,OAAOM,IAAI,CAAC,QAAQP;IACrC;IAEAD,MAAMC,KAAK,GAAGD,MAAMS,IAAI,GAAG,OAAOT,MAAMf,OAAO,GAAGgB;AACpD", "ignoreList": [0]}